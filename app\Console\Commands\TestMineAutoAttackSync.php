<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\MineAutoAttackJob;
use App\Services\MineDetectionService;
use App\Services\MineTargetDistributionService;
use App\Services\BattleLogService;
use App\Services\PlayerHealthService;
use App\Services\CombatFormulaService;
use App\Services\LogFormattingService;

class TestMineAutoAttackSync extends Command
{
    protected $signature = 'test:mine-auto-attack-sync';
    protected $description = 'Тестирует автоатаки мобов в рудниках синхронно';

    public function handle()
    {
        $this->info('🔍 Тестирование автоатак мобов в рудниках (синхронно)...');

        try {
            // Создаем экземпляр Job и выполняем его синхронно
            $job = new MineAutoAttackJob();
            
            // Получаем необходимые сервисы
            $mineDetectionService = app(MineDetectionService::class);
            $targetDistributionService = app(MineTargetDistributionService::class);
            $battleLogService = app(BattleLogService::class);
            $playerHealthService = app(PlayerHealthService::class);
            $combatFormulaService = app(CombatFormulaService::class);
            $logFormatter = app(LogFormattingService::class);

            $this->info('📋 Выполняем Job синхронно...');
            
            // Выполняем Job
            $job->handle(
                $mineDetectionService,
                $targetDistributionService,
                $battleLogService,
                $playerHealthService,
                $combatFormulaService,
                $logFormatter
            );

            $this->info('✅ Job выполнен успешно!');
            
            // Проверяем результат
            $this->info('🔍 Проверяем активные эффекты игрока admin...');
            
            $admin = \App\Models\User::where('name', 'admin')->first();
            if ($admin) {
                $effects = $admin->activeEffects;
                $this->info("📊 Всего эффектов: {$effects->count()}");
                
                foreach ($effects as $effect) {
                    $isActive = $effect->isActive();
                    $isStun = $effect->isStunEffect();
                    $this->info("  - Эффект ID {$effect->id}: {$effect->effect_type}, Активен: " . ($isActive ? 'Да' : 'Нет') . ", Стан: " . ($isStun ? 'Да' : 'Нет'));
                }
                
                $stunned = $effects->filter(function ($effect) {
                    return $effect->isActive() && $effect->isStunEffect();
                });
                
                $this->info("🔒 Игрок оглушен: " . ($stunned->count() > 0 ? 'ДА' : 'НЕТ'));
            }

            return 0;
        } catch (\Exception $e) {
            $this->error('❌ Ошибка при выполнении Job: ' . $e->getMessage());
            $this->error('Трассировка: ' . $e->getTraceAsString());
            return 1;
        }
    }
}
