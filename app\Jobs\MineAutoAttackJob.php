<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Models\Mob;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Services\MineDetectionService;
use App\Services\MineTargetDistributionService;
use App\Services\BattleLogService;
use App\Services\PlayerHealthService;
use App\Services\CombatFormulaService;
use App\Services\LogFormattingService;
use App\Services\Mine\MobSkillIntegrationService;
use App\Events\MobAttackedPlayer;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

/**
 * Job для автоматической атаки мобов на замеченных игроков в рудниках
 * Отдельная логика от обелисков для лучшей производительности
 */
class MineAutoAttackJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 120;

    /**
     * Выполнить задачу автоатаки мобов в рудниках
     */
    public function handle(
        MineDetectionService $mineDetectionService,
        MineTargetDistributionService $targetDistributionService,
        BattleLogService $battleLogService,
        PlayerHealthService $playerHealthService,
        CombatFormulaService $combatFormulaService,
        LogFormattingService $logFormatter
    ): void {
        $lockKey = 'mine_auto_attack_lock';
        $lockTimeout = 60;

        try {
            $lock = Redis::set($lockKey, time(), 'EX', $lockTimeout, 'NX');
            if (!$lock) {
                Log::info('MineAutoAttackJob: Другая задача автоатаки уже выполняется');
                return;
            }

            Log::info('MineAutoAttackJob: Начинаем обработку автоатак мобов в рудниках');

            // Очистка истекших меток перед обработкой
            $mineDetectionService->cleanupExpiredMarks();

            // Получаем всех замеченных игроков в рудниках
            $markedPlayers = $this->getActiveMarkedPlayers($mineDetectionService);

            if (empty($markedPlayers)) {
                Log::info('MineAutoAttackJob: Нет замеченных игроков в рудниках');
                return;
            }

            // Группируем игроков по локациям рудников для оптимизации
            $playersByLocation = $this->groupPlayersByLocation($markedPlayers);

            $attacksProcessed = 0;
            $maxAttacksPerRun = 20;

            foreach ($playersByLocation as $mineLocationId => $locationPlayers) {
                if ($attacksProcessed >= $maxAttacksPerRun) {
                    Log::info('MineAutoAttackJob: Достигнуто максимальное количество атак за раз');
                    break;
                }

                $mineLocation = $locationPlayers[0]['mine_location']; // Все игроки в одной локации

                // Очищаем истекшие назначения для этой локации
                $targetDistributionService->cleanupExpiredAssignments($mineLocation);

                // Используем умную систему распределения
                $mobPlayerPair = $targetDistributionService->getOptimalMobPlayerPair(
                    collect($locationPlayers),
                    $mineLocation
                );

                if ($mobPlayerPair) {
                    try {
                        $result = $this->executeSmartAttack(
                            $mobPlayerPair['mob'],
                            $mobPlayerPair['player'],
                            $mineLocation,
                            $mineDetectionService,
                            $battleLogService,
                            $playerHealthService,
                            $combatFormulaService,
                            $logFormatter
                        );

                        if ($result) {
                            $attacksProcessed++;
                            // Небольшая задержка между атаками
                            usleep(100000); // 0.1 секунды
                        }
                    } catch (\Exception $e) {
                        Log::error('MineAutoAttackJob: Ошибка при выполнении умной атаки', [
                            'mob_id' => $mobPlayerPair['mob']->id,
                            'player_id' => $mobPlayerPair['player']->id,
                            'mine_location_id' => $mineLocation->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }

            Log::info('MineAutoAttackJob: Завершена обработка автоатак', [
                'attacks_processed' => $attacksProcessed,
                'marked_players_found' => count($markedPlayers)
            ]);

        } catch (\Exception $e) {
            Log::error('MineAutoAttackJob: Критическая ошибка', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        } finally {
            Redis::del($lockKey);
        }
    }

    /**
     * Получить всех замеченных игроков в рудниках
     */
    private function getActiveMarkedPlayers(MineDetectionService $mineDetectionService): array
    {
        try {
            $marks = MineMark::with(['player.profile', 'mineLocation'])
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->get();

            $validPlayers = [];

            foreach ($marks as $mark) {
                // Проверки валидности
                if (
                    !$mark->player ||
                    !$mark->player->profile ||
                    !$mark->mineLocation ||
                    $mark->player->profile->current_hp <= 0
                ) {
                    continue;
                }

                // Проверяем, что игрок находится в нужной локации (через статистику)
                $playerLocation = $mark->player->statistics ? $mark->player->statistics->current_location : null;
                if ($playerLocation !== $mark->location_name) {
                    // Игрок покинул локацию, удаляем метку
                    $mineDetectionService->removeDetectionDebuff($mark->player_id, $mark->location_id);
                    continue;
                }

                $validPlayers[] = [
                    'mark_id' => $mark->id,
                    'player_id' => $mark->player_id,
                    'player' => $mark->player,
                    'mine_location_id' => $mark->mine_location_id,
                    'mine_location' => $mark->mineLocation,
                    'location_id' => $mark->location_id,
                    'location_name' => $mark->location_name,
                    'expires_at' => $mark->expires_at,
                    'last_attack_at' => $mark->last_attack_at,
                    'attack_count' => $mark->attack_count ?? 0
                ];
            }

            return $validPlayers;

        } catch (\Exception $e) {
            Log::error('MineAutoAttackJob: Ошибка при получении замеченных игроков', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Обработать атаку на конкретного игрока
     */
    private function processPlayerAttack(
        array $playerData,
        MineDetectionService $mineDetectionService,
        BattleLogService $battleLogService,
        PlayerHealthService $playerHealthService,
        CombatFormulaService $combatFormulaService,
        LogFormattingService $logFormatter
    ): bool {
        $player = $playerData['player'];
        $mineLocation = $playerData['mine_location'];

        // Проверяем кулдаун атак на этого игрока (минимум 30 секунд между атаками)
        if (
            $playerData['last_attack_at'] &&
            Carbon::parse($playerData['last_attack_at'])->addSeconds(30)->isFuture()
        ) {
            return false;
        }

        // Находим доступных мобов в руднике
        $availableMobs = $this->getAvailableMobsInMine($mineLocation);

        if ($availableMobs->isEmpty()) {
            Log::info('MineAutoAttackJob: Нет доступных мобов в руднике', [
                'mine_location_id' => $mineLocation->id,
                'location_name' => $mineLocation->name
            ]);
            return false;
        }

        // Выбираем случайного моба
        $mob = $availableMobs->random();

        // Рассчитываем урон
        $baseDamage = rand(5, 15);
        $damage = $combatFormulaService->calculateMobDamage($mob, $player, $baseDamage);

        // Применяем урон к игроку
        $damageResult = $playerHealthService->applyDamage(
            $player,
            $damage,
            "mine_mob_detection:{$mob->id}"
        );

        // Обрабатываем скиллы моба при атаке в руднике
        $mobSkillService = app(MobSkillIntegrationService::class);
        $activatedSkills = $mobSkillService->processMobAttackSkills($mob, $player, [
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'damage_dealt' => $damageResult['damage'],
            'attack_type' => 'mine_detection'
        ]);

        // Обновляем время последней атаки в метке
        $mineDetectionService->updateLastAttack($player->id, $mineLocation->id);

        // Создаем запись в боевом логе
        $logMessage = $logFormatter->formatMineDetectionAttack(
            $mob->name,
            $player->name,
            $damage,
            $mineLocation->name,
            $activatedSkills
        );

        // ИСПРАВЛЕНИЕ: Используем унифицированный ключ для рудников, как в CustomMineController
        $battleLogKey = "battle_logs:mines:{$player->id}";
        $battleLogService->addLog($battleLogKey, $logMessage, 'danger');

        // Запускаем событие атаки моба
        event(new MobAttackedPlayer($mob, $player, $damage, 'mine_detection'));

        Log::info('MineAutoAttackJob: Успешная атака моба на замеченного игрока', [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'player_id' => $player->id,
            'player_name' => $player->name,
            'damage' => $damage,
            'mine_location' => $mineLocation->name,
            'player_health_after' => $damageResult['current_health'] ?? 'unknown'
        ]);

        return true;
    }

    /**
     * Группировать игроков по локациям рудников
     */
    private function groupPlayersByLocation(array $markedPlayers): array
    {
        $grouped = [];

        foreach ($markedPlayers as $playerData) {
            $mineLocationId = $playerData['mine_location_id'];
            if (!isset($grouped[$mineLocationId])) {
                $grouped[$mineLocationId] = [];
            }
            $grouped[$mineLocationId][] = $playerData;
        }

        return $grouped;
    }

    /**
     * Выполнить умную атаку моба на игрока
     */
    private function executeSmartAttack(
        Mob $mob,
        User $player,
        MineLocation $mineLocation,
        MineDetectionService $mineDetectionService,
        BattleLogService $battleLogService,
        PlayerHealthService $playerHealthService,
        CombatFormulaService $combatFormulaService,
        LogFormattingService $logFormatter
    ): bool {
        // Рассчитываем урон
        $mobStrength = $mob->strength ?? 1;
        $playerArmor = $player->profile->getEffectiveStats()['armor'] ?? 0;
        $damage = $combatFormulaService->calculateDamage($mobStrength, $playerArmor);

        // Применяем урон к игроку
        $damageResult = $playerHealthService->applyDamage(
            $player,
            $damage,
            "mine_mob_smart_attack:{$mob->id}"
        );

        // Обрабатываем скиллы моба при атаке в руднике
        $mobSkillService = app(MobSkillIntegrationService::class);

        Log::info('MineAutoAttackJob: Обрабатываем скиллы моба', [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'player_id' => $player->id,
            'player_name' => $player->name,
            'damage_dealt' => $damageResult['damage']
        ]);

        $activatedSkills = $mobSkillService->processMobAttackSkills($mob, $player, [
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'damage_dealt' => $damageResult['damage'],
            'attack_type' => 'mine_smart_attack'
        ]);

        Log::info('MineAutoAttackJob: Результат активации скиллов', [
            'activated_skills_count' => count($activatedSkills),
            'activated_skills' => $activatedSkills
        ]);

        // Обновляем время последней атаки в метке
        $mineDetectionService->updateLastAttack($player->id, $mineLocation->id);

        // Создаем запись в боевом логе
        $logMessage = $logFormatter->formatMineDetectionAttack(
            $mob->name,
            $player->name,
            $damage,
            $mineLocation->name,
            $activatedSkills
        );

        // ИСПРАВЛЕНИЕ: Используем унифицированный ключ для рудников, как в CustomMineController
        $battleLogKey = "battle_logs:mines:{$player->id}";
        $battleLogService->addLog($battleLogKey, $logMessage, 'danger');

        // Запускаем событие атаки моба
        event(new MobAttackedPlayer($mob, $player, $damage, 'mine_smart_detection'));

        Log::info('MineAutoAttackJob: Умная атака моба на замеченного игрока', [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'player_id' => $player->id,
            'player_name' => $player->name,
            'damage' => $damage,
            'mine_location' => $mineLocation->name,
            'player_health_after' => $damageResult['current_health'] ?? 'unknown'
        ]);

        return true;
    }

    /**
     * Получить доступных мобов в руднике
     */
    private function getAvailableMobsInMine(MineLocation $mineLocation)
    {
        return Mob::where('location_id', $mineLocation->location_id)
            ->where('mob_type', 'mine') // Фильтр только рудничных мобов
            ->where('hp', '>', 0) // Исправлено поле hp
            ->where(function ($query) {
                $query->whereNull('death_time')
                    ->orWhere('death_time', '<', now()->subMinutes(5));
            })
            ->get();
    }
}